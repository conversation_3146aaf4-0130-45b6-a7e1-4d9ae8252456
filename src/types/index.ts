import { BigNumberish } from 'ethers';

export interface Config {
  rpcUrl: string;
  flashbotsRpcUrl: string;
  chainId: number;
  privateKey: string;
  flashbotsSignerKey: string;
  minProfitWei: string;
  maxGasPriceGwei: number;
  maxPriorityFeeGwei: number;
  slippageTolerance: number;
  mempoolWebsocketUrl: string;
  enableFlashbotsMempool: boolean;
  enableEthersMempool: boolean;
  enableSandwichAttacks: boolean;
  enableFrontRunning: boolean;
  enableArbitrage: boolean;
  enableFlashloanAttacks: boolean;
  enableMultiBlockAttacks: boolean;
  maxBlocksAhead: number;
  maxPositionSizeEth: number;
  emergencyStop: boolean;
  dryRun: boolean;
  logLevel: string;
  logToFile: boolean;
  // Flashloan contract addresses
  hybridFlashloanContract: string;
  balancerFlashloanContract: string;
  aaveFlashloanContract: string;
  // Flashloan DEX configuration
  flashloanDexPairs: string[];
  flashloanBuyDex: string;
  flashloanSellDex: string;
  enableCrossDexArbitrage: boolean;
  minArbitrageSpread: number;
  // Flashloan token configuration
  flashloanTokens: string[];
  flashloanPrimaryToken: string;
  flashloanTargetTokens: string[];
  enableAllTokenPairs: boolean;
  minTokenLiquidityUsd: number;
  // Flashbots configuration
  enableFlashbots: boolean;
  flashbotsRelayUrl: string;
  flashbotsAuthKey: string;
  // MEV-Share configuration
  enableMevShare: boolean;
  mevShareStreamUrl: string;
  enableBackrunStrategy: boolean;
  minBackrunProfitEth: number;
  maxGasCostEth: number;
  // Scanning intervals
  arbitrageScanIntervalMs: number;
  flashloanScanIntervalMs: number;
  // Advanced gas estimation
  blocknativeApiKey: string;
  enableBlocknativeGas: boolean;
  enable0xApiGas: boolean;
  enableEthGasStation: boolean;
  fallbackGasPriceGwei: number;
}

export interface Token {
  address: string;
  symbol: string;
  decimals: number;
  name: string;
}

export interface Pool {
  address: string;
  token0: Token;
  token1: Token;
  fee: number;
  protocol: 'uniswap-v2' | 'uniswap-v3' | 'balancer' | 'curve';
  reserves?: {
    reserve0: BigNumberish;
    reserve1: BigNumberish;
  };
  liquidity?: BigNumberish;
  tick?: number;
  sqrtPriceX96?: BigNumberish;
  // Curve-specific properties
  curveTokenIndices?: {
    i: number; // Index of token0 in Curve pool
    j: number; // Index of token1 in Curve pool
  };
}

export interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: BigNumberish;
  gasPrice: BigNumberish;
  gasLimit: BigNumberish;
  data: string;
  nonce: number;
  maxFeePerGas?: BigNumberish;
  maxPriorityFeePerGas?: BigNumberish;
}

export interface DecodedSwap {
  method: string;
  protocol: string;
  tokenIn: Token;
  tokenOut: Token;
  amountIn: BigNumberish;
  amountOutMin: BigNumberish;
  recipient: string;
  deadline: number;
  path: string[];
  fee?: number;
}

export interface MEVOpportunity {
  type: 'sandwich' | 'frontrun' | 'arbitrage' | 'flashloan';
  victimTx: Transaction;
  decodedSwap: DecodedSwap;
  pool: Pool;
  estimatedProfit: BigNumberish;
  gasEstimate: BigNumberish;
  frontRunTx?: Transaction;
  backRunTx?: Transaction;
  confidence: number;
  timestamp: number;
}

export interface Bundle {
  transactions: Transaction[];
  blockNumber: number;
  minTimestamp?: number;
  maxTimestamp?: number;
  revertingTxHashes?: string[];
}

export interface SimulationResult {
  success: boolean;
  gasUsed: BigNumberish;
  profit: BigNumberish;
  error?: string;
  bundleHash?: string;
}

export interface GasStrategy {
  baseFee: BigNumberish;
  priorityFee: BigNumberish;
  maxFeePerGas: BigNumberish;
  gasLimit: BigNumberish;
}

export interface ArbitrageRoute {
  pools: Pool[];
  tokens: Token[];
  expectedProfit: BigNumberish;
  gasEstimate: BigNumberish;
  confidence: number;
}

export interface FlashloanRoute {
  flashloanToken: Token;
  flashloanAmount: BigNumberish;
  flashloanPremium: BigNumberish;
  arbitrageRoute: ArbitrageRoute;
  expectedProfit: BigNumberish;
  gasEstimate: BigNumberish;
  confidence: number;
}

export interface FlashloanParams {
  asset: string;
  amount: BigNumberish;
  premium: BigNumberish;
  initiator: string;
  params: string;
}

export interface LiquidityData {
  totalLiquidity: BigNumberish;
  priceImpact: number;
  optimalAmountIn: BigNumberish;
  expectedAmountOut: BigNumberish;
}

export interface MempoolFilter {
  minValue: BigNumberish;
  maxGasPrice: BigNumberish;
  targetTokens: string[];
  targetPools: string[];
  excludeAddresses: string[];
}

export interface BotState {
  isRunning: boolean;
  totalProfit: BigNumberish;
  successfulTrades: number;
  failedTrades: number;
  lastActivity: number;
  emergencyStop: boolean;
}

export interface RiskMetrics {
  maxDrawdown: BigNumberish;
  winRate: number;
  averageProfit: BigNumberish;
  totalGasSpent: BigNumberish;
  profitFactor: number;
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: number;
  data?: any;
}
