#!/usr/bin/env node

/**
 * Diagnose why opportunities are not being found
 * Checks configuration, token balances, and opportunity detection
 */

const { ethers } = require('ethers');
const chalk = require('chalk');

console.log(chalk.blue.bold('\n🔍 MEV Opportunity Diagnostic Tool\n'));

async function diagnoseOpportunities() {
  try {
    // Load configuration
    require('dotenv').config();
    
    console.log(chalk.yellow.bold('📋 Configuration Analysis'));
    console.log(chalk.gray('─'.repeat(50)));
    
    // Check environment variables
    const config = {
      chainId: process.env.CHAIN_ID,
      primaryToken: process.env.FLASHLOAN_PRIMARY_TOKEN,
      targetTokens: process.env.FLASHLOAN_TARGET_TOKENS,
      minProfitWei: process.env.MIN_PROFIT_WEI,
      dryRun: process.env.DRY_RUN,
      enableFlashloan: process.env.ENABLE_FLASHLOAN_ATTACKS,
      privateKey: process.env.PRIVATE_KEY
    };
    
    console.log(`${chalk.cyan('Chain ID:')}           ${chalk.white(config.chainId)} ${config.chainId === '11155111' ? chalk.green('(Sepolia ✓)') : chalk.red('(Not Sepolia!)')}`);
    console.log(`${chalk.cyan('Primary Token:')}      ${chalk.white(config.primaryToken)}`);
    console.log(`${chalk.cyan('Target Tokens:')}      ${chalk.white(config.targetTokens)}`);
    console.log(`${chalk.cyan('Min Profit:')}         ${chalk.white(ethers.formatEther(config.minProfitWei || '0'))} ETH`);
    console.log(`${chalk.cyan('Dry Run:')}            ${config.dryRun === 'true' ? chalk.green('Enabled (Safe)') : chalk.red('Disabled (Live)')}`);
    console.log(`${chalk.cyan('Flashloan Enabled:')}  ${config.enableFlashloan === 'true' ? chalk.green('Yes') : chalk.red('No')}`);
    
    // Check wallet setup
    console.log(chalk.yellow.bold('\n💰 Wallet Analysis'));
    console.log(chalk.gray('─'.repeat(50)));
    
    if (!config.privateKey || config.privateKey.length !== 66) {
      console.log(chalk.red('❌ Invalid private key format'));
      return;
    }
    
    // Connect to Sepolia
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    const wallet = new ethers.Wallet(config.privateKey, provider);
    
    console.log(`${chalk.cyan('Wallet Address:')}     ${chalk.white(wallet.address)}`);
    
    // Check ETH balance
    const ethBalance = await provider.getBalance(wallet.address);
    const ethBalanceFormatted = ethers.formatEther(ethBalance);
    console.log(`${chalk.cyan('ETH Balance:')}        ${chalk.white(ethBalanceFormatted)} ETH ${parseFloat(ethBalanceFormatted) > 0.1 ? chalk.green('(Sufficient)') : chalk.red('(Low!)')}`);
    
    // Check if we can get WETH balance (if WETH is primary token)
    if (config.primaryToken === 'WETH') {
      try {
        // Sepolia WETH contract address
        const wethAddress = '******************************************'; // Sepolia WETH
        const wethAbi = [
          'function balanceOf(address) view returns (uint256)',
          'function symbol() view returns (string)',
          'function decimals() view returns (uint8)'
        ];
        
        const wethContract = new ethers.Contract(wethAddress, wethAbi, provider);
        const wethBalance = await wethContract.balanceOf(wallet.address);
        const wethBalanceFormatted = ethers.formatEther(wethBalance);
        
        console.log(`${chalk.cyan('WETH Balance:')}       ${chalk.white(wethBalanceFormatted)} WETH ${parseFloat(wethBalanceFormatted) > 0 ? chalk.green('(Available)') : chalk.yellow('(None - need to wrap ETH)')}`);
        
        if (parseFloat(wethBalanceFormatted) === 0) {
          console.log(chalk.yellow('\n💡 Suggestion: You need to wrap some ETH to WETH for flashloan arbitrage'));
          console.log(chalk.gray('   Visit: https://sepolia.etherscan.io/address/******************************************#writeContract'));
          console.log(chalk.gray('   Or use a DEX to swap ETH → WETH'));
        }
        
      } catch (error) {
        console.log(chalk.red('❌ Could not check WETH balance'));
      }
    }
    
    // Check network connectivity
    console.log(chalk.yellow.bold('\n🌐 Network Analysis'));
    console.log(chalk.gray('─'.repeat(50)));
    
    try {
      const blockNumber = await provider.getBlockNumber();
      const block = await provider.getBlock(blockNumber);
      
      console.log(`${chalk.cyan('Current Block:')}      ${chalk.white(blockNumber)}`);
      console.log(`${chalk.cyan('Block Timestamp:')}    ${chalk.white(new Date(block.timestamp * 1000).toLocaleString())}`);
      console.log(`${chalk.cyan('Network Status:')}     ${chalk.green('Connected ✓')}`);
      
      // Check recent transaction activity
      const recentTxCount = block.transactions.length;
      console.log(`${chalk.cyan('Block Transactions:')} ${chalk.white(recentTxCount)} ${recentTxCount > 10 ? chalk.green('(Active)') : chalk.yellow('(Low activity)')}`);
      
    } catch (error) {
      console.log(chalk.red('❌ Network connection failed'));
      console.log(chalk.red(`   Error: ${error.message}`));
    }
    
    // Analyze opportunity detection issues
    console.log(chalk.yellow.bold('\n🎯 Opportunity Detection Analysis'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const issues = [];
    const suggestions = [];
    
    // Check minimum profit threshold
    const minProfitEth = parseFloat(ethers.formatEther(config.minProfitWei || '0'));
    if (minProfitEth > 0.01) {
      issues.push(`Min profit too high: ${minProfitEth} ETH`);
      suggestions.push('Lower MIN_PROFIT_WEI to 1000000000000000 (0.001 ETH)');
    } else {
      console.log(`${chalk.cyan('Min Profit Threshold:')} ${chalk.green('✓ Low enough')} (${minProfitEth} ETH)`);
    }
    
    // Check if flashloan is enabled
    if (config.enableFlashloan !== 'true') {
      issues.push('Flashloan attacks disabled');
      suggestions.push('Set ENABLE_FLASHLOAN_ATTACKS=true');
    } else {
      console.log(`${chalk.cyan('Flashloan Strategy:')}   ${chalk.green('✓ Enabled')}`);
    }
    
    // Check token configuration
    if (config.primaryToken === 'USDC' && parseFloat(ethBalanceFormatted) > 0) {
      issues.push('Using USDC as primary but only have ETH');
      suggestions.push('Change FLASHLOAN_PRIMARY_TOKEN=WETH or get USDC tokens');
    } else if (config.primaryToken === 'WETH') {
      console.log(`${chalk.cyan('Token Strategy:')}      ${chalk.green('✓ WETH-based')} (good for ETH holders)`);
    }
    
    // Check Sepolia-specific issues
    if (config.chainId === '11155111') {
      console.log(`${chalk.cyan('Testnet Considerations:')} ${chalk.yellow('Sepolia has lower activity')}`);
      issues.push('Sepolia has limited DEX activity');
      suggestions.push('Consider testing on mainnet fork or get testnet tokens');
    }
    
    // Display issues and suggestions
    if (issues.length > 0) {
      console.log(chalk.red.bold('\n❌ Issues Found:'));
      issues.forEach(issue => {
        console.log(chalk.red(`   • ${issue}`));
      });
      
      console.log(chalk.blue.bold('\n💡 Suggestions:'));
      suggestions.forEach(suggestion => {
        console.log(chalk.blue(`   • ${suggestion}`));
      });
    } else {
      console.log(chalk.green.bold('\n✅ Configuration looks good!'));
    }
    
    // Quick fix recommendations
    console.log(chalk.yellow.bold('\n🔧 Quick Fixes for Sepolia:'));
    console.log(chalk.gray('─'.repeat(50)));
    console.log('1. Get Sepolia WETH:');
    console.log('   • Visit Sepolia WETH contract: ******************************************');
    console.log('   • Call "deposit" function with some ETH to get WETH');
    console.log('');
    console.log('2. Lower profit threshold:');
    console.log('   • Set MIN_PROFIT_WEI=1000000000000000 (0.001 ETH)');
    console.log('');
    console.log('3. Enable more strategies:');
    console.log('   • Set ENABLE_ARBITRAGE=true');
    console.log('   • Set ENABLE_SANDWICH_ATTACKS=true');
    console.log('');
    console.log('4. Check for testnet DEX activity:');
    console.log('   • Sepolia has limited real trading');
    console.log('   • Consider mainnet fork for testing');
    
  } catch (error) {
    console.log(chalk.red(`\n❌ Diagnostic failed: ${error.message}`));
  }
}

// Run diagnostic
diagnoseOpportunities().then(() => {
  console.log(chalk.blue.bold('\n🏁 Diagnostic Complete\n'));
}).catch(error => {
  console.log(chalk.red(`\nDiagnostic error: ${error.message}\n`));
});
